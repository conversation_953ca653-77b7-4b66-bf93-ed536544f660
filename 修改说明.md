# 关于稿件的修改说明

尊敬的编辑和专家：

您好！

非常感谢审稿专家的宝贵意见和编辑部的辛苦工作！作者根据审稿意见对文章内容做了修改和完善，并将所修改部分进行了红色字体标记。此外，作者对文章言语叙述部分做了完善并使用红色字体标记。对审稿意见的具体答复如下：

## 1. 摘要中对算法性能的描述缺少量化数据。

**答复：** 非常感谢审稿人提出的宝贵意见！针对您提到的"摘要中对算法性能的描述缺少量化数据"的意见，我们非常重视并已进行了仔细的修改。我们认识到在摘要中提供具体的量化数据对于读者快速了解研究成果的重要性，这有助于更直观地展示我们方法的有效性和优越性。

为此，我们已经对摘要部分进行了修改，在原有定性描述的基础上增加了关键的量化指标，在修改稿中已用红色注明。

**修改如下：**

见修改稿摘要部分

我们设计了一种新颖的基于扩散模型的时间步自适应调谐方法(EvoTune)，动态调整U-Net在图像生成过程中的特征贡献，**在SSIM、PNSR、NIQE指标上分别达到0.9188、26.7902、5.7133**，显著提升了积水区域的生成质量与真实感，有效扩充了数据集中积水干扰场景的样本数量与多样性。

大量实验结果表明，EvoTune显著提升了训练数据的质量；而HyDR-Net**在F1、PA指标上分别达到80.46%、92.15%**，在各项关键评价指标上均大幅超越了现有主流分割方法，尤其在复杂积水干扰场景下展现出卓越的分割精度与鲁棒性。

同时，我们也对英文摘要进行了相应的修改：

We design a novel diffusion models-based timestep adaptive tuning method (EvoTune) that dynamically adjusts the feature contributions of U-Net during image generation, **achieving SSIM, PNSR, and NIQE scores of 0.9188, 26.7902, and 5.7133 respectively**, significantly improving the generation quality and realism of water accumulation regions while effectively expanding the quantity and diversity of water interference scenarios in the dataset.

Extensive experimental results demonstrate that EvoTune significantly improves training data quality, while HyDR-Net **achieves F1 and PA scores of 80.46% and 92.15% respectively**, substantially outperforms existing mainstream segmentation methods across all key evaluation metrics, particularly exhibiting superior segmentation accuracy and robustness in complex water interference scenarios.

**详细说明：**

1. **EvoTune方法的量化指标：** 我们在摘要中明确给出了EvoTune在图像生成质量评估方面的具体数值，包括结构相似性指数(SSIM)为0.9188、峰值信噪比(PNSR)为26.7902、自然图像质量评估(NIQE)为5.7133，这些指标充分证明了我们的数据增强方法在提升生成图像质量方面的显著效果。

2. **HyDR-Net网络的性能指标：** 我们补充了网络在关键评价指标上的具体数值，F1分数达到80.46%，像素准确率(PA)达到92.15%，这些量化数据直观地展现了我们提出的分割网络的优越性能。

这些量化数据的加入使得摘要更加具体和有说服力，能够让读者在第一时间了解到我们研究成果的具体表现，同时也为后续的详细实验分析提供了有力的支撑。

再次感谢您的指正，您的意见对于提升论文质量非常有帮助。如果您有其他建议或意见，我们将非常愿意进一步修改和完善。

---

衷心感谢审稿专家的宝贵意见！您的反馈对我们提升文章质量至关重要。我们认真考虑并采纳了您的建议，对文章进行了必要的修改和完善。期待在修改稿中继续得到您的指导和支持！

此致
敬礼！

作者
[日期]
