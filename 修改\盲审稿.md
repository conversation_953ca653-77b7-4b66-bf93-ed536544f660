# 面向积水干扰的变电设备渗漏油精准分割方法

摘要：变电设备渗漏油的准确分割对于保障电力系统安全运行至关重要，然而，渗漏油与积水的高度视觉相似性、自身形态的不规则性以及此类干扰场景下训练数据的匮乏，对现有分割方法构成了严峻挑战。为应对这一挑战，本文提出了一种从数据增强到网络模型优化的综合解决方案。我们设计了一种新颖的基于扩散模型的时间步自适应调谐方法(EvoTune)，动态调整U-Net在图像生成过程中的特征贡献，显著提升了积水区域的生成质量与真实感，有效扩充了数据集中积水干扰场景的样本数量与多样性。其次，在数据增强的基础上，我们提出了一种高性能的渗漏油分割网络HyDR-Net。该网络通过创新性的判别式边界抗扰模块(DBAIM)，增强渗漏油与积水等易混淆背景的特征判别能力，并有效抑制背景噪声；以及创新性的多尺度注意力校准模块(MAAM)，对渗漏油特征进行多尺度上下文感知和精细化的边界校准，以适应渗漏油不规则的形态。大量实验结果表明，EvoTune显著提升了训练数据的质量；而HyDR-Net在各项关键评价指标上均大幅超越了现有主流分割方法，尤其在复杂积水干扰场景下展现出卓越的分割精度与鲁棒性。本研究为解决特定视觉干扰下的数据稀缺问题提供了有效途径，并为变电设备渗漏油的智能、精准检测提供了强有力的技术支撑。

关键词：渗漏油分割；变电设备；数据增强；扩散模型；积水干扰

中图分类号：TP 391         **DOI**：10.11996/JG.j.2095-302X.0000000000

文献标识码：A          文 章 编 号：2095-302X(0000)00-0000-00

**Precise Oil Leakage Segmentation for Substation Equipment Under Water Accumulation Interference**

**Abstract:** Accurate segmentation of oil leakage from substation equipment is crucial for ensuring the safe operation of power systems. However, existing segmentation methods face significant challenges due to the high visual similarity between oil leakage and water accumulation, the irregular morphology of oil spills, and the scarcity of training data in such interference scenarios. To address these challenges, this paper proposes a comprehensive solution encompassing both data augmentation and network model optimization. We design a novel diffusion models-based timestep adaptive tuning method (EvoTune) that dynamically adjusts the feature contributions of U-Net during image generation, significantly improving the generation quality and realism of water accumulation regions while effectively expanding the quantity and diversity of water interference scenarios in the dataset. Building upon this data augmentation foundation, we propose HyDR-Net (Hydro Discriminative Refining Network), a high-performance oil leakage segmentation network. The network incorporates an innovative Discriminative Boundary Anti-interference Module (DBAIM) that enhances feature discrimination between oil leakage and confusing backgrounds such as water accumulation while effectively suppressing background noise. Additionally, a Multi-Scale Attentive Alignment Module (MAAM) performs multi-scale context-aware processing and fine-grained boundary calibration of oil leakage features to accommodate the irregular morphology of oil spills. Extensive experimental results demonstrate that EvoTune significantly improves training data quality, while HyDR-Net substantially outperforms existing mainstream segmentation methods across all key evaluation metrics, particularly exhibiting superior segmentation accuracy and robustness in complex water interference scenarios. This research provides an effective approach for addressing data scarcity under specific visual interference conditions and offers robust technical support for intelligent and precise detection of oil leakage in substation equipment.

**Keywords:** oil leakage segmentation；substation equipment；data augmentation；diffusion models；water accumulation interference 



1  引言

在“双碳”目标驱动背景下，以大规模新能源接入为特征的新型电力系统正经历深刻变革，电网的复杂性与不确定性显著增加，对电力系统智能化提出了更高要求[1,2]。变电站作为电力系统的核心枢纽，其运行状态直接决定整个电力系统的安全稳定性。其中变压器、互感器等充油变电设备是变电站中数量众多且功能关键的核心组件，承担着电压变换和电能分配等重要任务。然而，受制造工艺、材料老化、运行环境等多种因素影响，这些设备在长期运行过程中不可避免地会发生渗漏油现象。渗漏油会导致设备油位下降，使设备在低油状态下运行，进而可能引发绝缘性能降低、内部过热、甚至短路、火灾爆炸等严重后果，对电力系统安全构成重大威胁。

近年来，变电站的运维模式正从传统的无人值守向数字化、智能化方向快速转型。基于智能监控系统和巡检机器人的自动化巡检已成为变电站运维的主流趋势[3]。然而在实际应用中，基于视觉的渗漏油检测仍面临严峻挑战。一方面，渗漏油本身形态不规则、边界模糊，且因其设备的吸附性其颜色、纹理特征往往与设备本体相似，增加了精确分割的难度。另一方面，也是更为棘手的问题，在于潮湿环境积水干扰下的视觉混淆。渗漏油与地面积水在视觉表现上存在高度相似性，导致传统的分割网络难以准确区分，极易产生大量的误检和漏检。这种混淆不仅降低了检测的可靠性，也严重影响了智能化巡检系统的实用价值。

当前基于深度学习的渗漏油分割研究主要面临以下两大挑战：一是特定干扰场景下高质量训练数据的匮乏，虽然晴朗天气下采集的巡检图像相对容易获取，但包含潮湿积水环境等特定视觉干扰的渗漏油图像却极为稀缺。这种数据分布的不均衡和多样性不足，使得训练出的模型在面对真实的复杂环境时泛化能力不足，性能急剧下降。但是直接在变电站大规模采集包含各类复杂天气和环境干扰的渗漏油图像，不仅成本高昂、操作困难，而且难以覆盖所有可能的干扰情况，少量样本又难以支撑深度学习模型的有效训练及定量评估[4]。因此，如何高效地生成高质量、高真实感的积水干扰场景下的渗漏油图像，以扩充训练数据集，成为提升模型鲁棒性的关键瓶颈。二是渗漏油与积水等干扰物的高相似性导致分割精度不高，即使拥有一定数量的训练数据，渗漏油与积水在颜色、光泽、不规则形态等视觉特征上的高度相似性，依然对分割算法提出了极高要求。现有的通用语义分割网络或针对渗漏油设计的模型，在处理这类细微特征差异时往往能力不足。网络在特征融合过程中，容易丢失关键的细节信息，同时很难充分利用这些差异特征进行有效的区分[5-7]，导致分割结果边缘模糊、区域错判。准确识别分割出渗漏油区域，同时有效抑制积水的影响，是当前渗漏油分割技术亟待解决的核心问题。

针对上述挑战，本文从数据增强和网络模型设计两个层面入手，旨在提升变电设备渗漏油在积水干扰下的分割性能。主要工作与贡献如下：

1）提出一种基于扩散模型的时间步自适应调谐数据增强方法(EvoTune)。为解决积水干扰场景下训练数据稀缺的问题，本文创新性地在扩散模型生成过程中引入时间步调谐机制。通过动态调整U-Net在图像生成过程中的特征贡献，EvoTune能够显著增强积水等高频细节的生成质量和真实感，有效扩充了数据集中积水干扰场景的样本，为后续分割模型的训练提供了更具针对性的数据支持。

2）提出了一种名为HyDR-Net(Hydro Discriminative Refining Network, HyDR-Net)的深度学习网络。该网络通过其核心的判别式边界抗扰模块(Discriminative Boundary Anti-interference Module, DBAIM)，专注于增强渗漏油与积水之间的特征判别能力，并有效抑制背景噪声。同时，利用多尺度注意力校准模块(Multi-Scale Attentive Alignment Module, MAAM)对渗漏油特征进行多尺度上下文感知和精细化的边界校准，以适应渗漏油不规则的形态并精确捕捉其边界。

2  相关工作

2.1 基于扩散模型的图像生成

扩散模型(Diffusion Models, DMs)作为新一代图像生成技术，因其出色的生成质量和多样性，已成为多个领域的研究焦点[8]。其核心机制在于通过一个前向加噪过程和一个反向去噪学习过程，实现从随机噪声到高质量图像的生成[9]。相较于生成对抗网络(GANs)和变分自编码器(VAEs)，扩散模型在图像细节、样本多样性及训练稳定性方面展现出显著优势[10]。许多先进的文生图模型，如Stable Diffusion(SD)[11]，也基于扩散思想，通过CLIP等文本编码器引导图像生成。

扩散模型已广泛应用于图像编辑、修复、超分辨率等任务[12]。CGDiff[13]通过在去噪早期阶段引入颜色引导，实现了对无条件扩散模型生成图像色彩的有效控制。在医学影像领域，结合DreamBooth[14]等技术，扩散模型能够生成高度逼真的癌症相关医学影像，用于数据增强和隐私保护[15]。针对遥感等高分辨率图像生成，轻量化扩散模型(LWTDM)通过引入DDIM等加速采样技术，提升了推理效率[16]。

尽管扩散模型取得了巨大成功，然而，当直接应用于特定工业场景，如本文关注的变电站潮湿积水环境下的渗漏油图像生成时，标准扩散模型如Dall-E 2[17]、Stable Diffusion、Imagen[18]在水渍纹理细节表现和动态反射效果方面仍存在不足。**如第4.1.2节的对比实验所示，这些标准模型在生成变电站积水干扰场景时，SSIM指标普遍低于0.74，PSNR低于23.2 dB，NIQE高于7.2，表明生成的图像在特定工业环境的细节表现和真实感方面难以满足高质量检测模型训练的需求。**虽然LoRA[19] (Low-Rank Adaptation)等技术能够通过少量样本快速实现风格迁移[20]，但在处理水面质感这类复杂视觉特征时依然存在局限性。此外，以Stable Diffusion为代表的主流模型采用固定的时间步调度策略，这种"一刀切"的方式无法适应专业工业场景的应用中不同生成阶段的特定需求。因此，如何针对特定场景优化扩散模型的生成细节，是提升其在专业领域应用价值的关键。

2.2 变电设备的渗漏油分割

针对变电设备渗漏油的检测与分割，现有研究主要集中在网络架构优化和特征提取改进两个方向。在网络架构方面，Li等[21]提出了基于U-Net的DAttRes-Unet模型，通过在解码器中集成空间注意力和通道注意力模块来抑制背景干扰，但该方法仅在荧光图像上验证，对自然光照下的复杂环境适应性有限。Geng等[22]提出的ACPA-Net通过空洞通道金字塔注意力模块捕捉多尺度特征，但其全局特征提取能力有限，在处理隧道不规则渗漏油区域时存在预测不完整的问题。研究者们还通过多阶段网络[23]、特征金字塔设计[24]以及图推理机制[25]来提升分割精度，但现有方法大多基于通用的语义分割框架，缺乏对渗漏油特定视觉特征的深度理解。更为关键的是，现有文献中，无论是海上溢油分割[26-29]还是隧道渗漏检测，都主要关注目标物体与一般背景的区分，而针对渗漏油与积水这类高相似性干扰物的判别机制研究几乎空白。**现有的通用语义分割方法（如FCN、U-Net、DeepLab系列等）主要基于卷积神经网络的层次化特征提取机制，通过学习目标与背景之间的显著特征差异来实现分割。这些方法的设计初衷是处理类别间差异明显的分割任务，其特征提取策略通常依赖于颜色、纹理、形状等低层视觉特征的组合以及高层语义特征。然而，在处理渗漏油与积水这类高相似性目标时，这些通用策略面临三个核心技术挑战：首先，在特征判别能力方面，由于渗漏油与积水在颜色分布、表面反射特性、边界形态等方面存在高度相似性，基于常规卷积操作的特征提取难以捕获渗漏油特有的粘稠感轮廓、油膜虹彩现象等细微差异特征，缺乏专门的差异感知机制来学习和放大这些关键的判别性特征；其次，在边界分割精度方面，传统的多尺度特征融合方法在处理渗漏油不规则且模糊的边界时容易导致边界信息失真，缺乏针对性的边界精炼策略来动态校准和优化边界定位；最后，在抗干扰能力方面，现有方法无法有效抑制积水区域的均匀反光、水渍边缘毛细现象等背景噪声对渗漏油分割的干扰，缺乏专门的抗干扰设计来主动抑制相似干扰因素。此外，现有数据集中包含积水干扰的渗漏油样本极为稀少，导致模型缺乏充分样本来学习区分这两类相似目标的判别性特征，而传统的交叉熵损失和Dice损失主要针对类别间差异显著的分割任务设计，在处理类间特征差异微小的情况时难以提供足够的梯度信号。这些技术局限性的客观存在可以通过实验数据得到验证：在我们构建的积水干扰场景数据集上，现有最优方法DSACP的F1分数仅为77.95%。针对上述挑战，本文提出的HyDR-Net通过判别式边界抗扰模块（DBAIM）中的差异感知边界精炼单元（DABRU）专门学习渗漏油与积水的细微差异特征，通过多尺度注意力校准模块（MAAM）实现对不规则边界的精确校准，并通过DBAIM的整体抗干扰机制有效抑制积水等背景噪声，最终在F1分数上达到了80.46%，相比现有方法提升了2.51%，充分证明了针对性技术设计的必要性和有效性**。

在数据层面上现有研究面临的另一个关键瓶颈是特定干扰场景下高质量训练数据的极度匮乏。虽然Chen等[26]通过仿射变换等传统数据增强方法扩充了海上溢油数据集，Fan等[29]采用师生网络结构进行跨域适应，但这些方法本质上仍是对现有数据的重新组合或迁移，无法从根本上解决积水干扰场景样本稀缺的问题。此外，与海上溢油或隧道渗漏不同，变电站环境下的渗漏油检测必须应对积水干扰这一特殊挑战。**在变电站渗漏油检测的应用语境下，具有"真实效果"的训练样本应满足以下标准：（1）视觉逼真度方面，生成的积水区域应具备真实积水的表面反射特性、边缘形态和纹理细节；（2）场景一致性方面，积水的空间分布、尺寸比例应与变电站实际环境相符；（3）训练有效性方面，生成样本应能有效提升模型对积水干扰场景的泛化能力。然而，传统的数据增强方法（如几何变换、颜色调整等）基于对现有图像的全局或像素级变换，其技术原理决定了这些方法只能重新排列或调整已有的视觉元素，而不能生成新的视觉内容。具体而言，在构建积水干扰的渗漏油场景时，传统方法存在以下技术局限性：无法在指定区域生成具有真实积水视觉特征的新内容，难以控制生成内容与原始场景的融合效果，且无法保证生成的积水区域在光照、透视等方面与原始图像保持一致。相比之下，基于扩散模型的局部重绘技术能够在保持原始渗漏油特征不变的前提下，在选定区域生成视觉逼真的积水内容，从而构建满足上述真实效果标准的训练样本**，而现有的生成模型又缺乏对这类专业场景的针对性优化。

综上所述，尽管现有研究在变电设备渗漏油检测与分割方面取得了一定进展，但在应对积水等视觉干扰下的数据稀缺问题，以及提升模型对渗漏油与高相似性干扰物的判别能力和边界分割精度方面，仍存在较大的提升空间。这正是本研究致力于解决的核心问题

3  研究方法

3.1  时间步自适应调谐方法

针对数据集中积水干扰样本稀缺的问题，本文提出了一种名为EvoTune的数据增强方法。该方法的核心创新在于将EvoTune设计为UNet内部的智能控制器，集成到Stable Diffusion的扩散去噪过程中，进行时间步感知的动态调谐，实现对积水区域生成过程的精确控制。EvoTune通过统一的时间步调谐框架，协同控制Agent机制、自适应频域处理三个核心组成，使模型能够在扩散过程的不同阶段采用最适合的处理策略，从而生成更真实、细节更丰富的积水干扰图像。该方法的整体架构如图1所示，系统以UNet编码器提取的特征和时间步为输入，通过时间步感知调谐器生成动态参数，驱动三个组成部分在UNet的关键连接点发挥作用，最终通过UNet解码器生成高质量的积水区域。

给定当前时间步*t*，系统首先进行时间步标准化，公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps2.png)

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps3.png)

其中*t* ∈ [0, *T*]，***T***=1000为总时间步数，*σ*(·)为Sigmoid激活函数，*φ*early(*t*)和*φ*late(*t*)分别为早期和后期相位函数，将时间步映射到[0,1]区间，并计算扩散过程不同阶段的激活强度。早期相位在***t***接近1000时激活，后期相位在***t***接近0时激活。为后续的UNet特征调制提供时间步感知的基础信号，确保积水区域的生成策略能够随扩散阶段动态调整。此后基于相位函数，系统采用统一的向量化公式生成时间步感知的参数，公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps8.png)

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps9.png)

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps10.png)

其中***b***0，***s***0，分别为特征和频率滤波基础权重向量，*φ*(*t*)为相位函数向量，*α*为调谐系数向量，*β*为进化强度参数，***w***0为注意力基础权重，***w****s*为注意力缩放因子, *γ*为注意力相位函数的陡峭度参数，*τ*为注意力激活阈值，⊙为Hadamard积。***b*** (*t*)含有两个分量***b***1(*t*)，***b***2(*t*)，分别用于增强骨干特征，建立积水区域的基础结构，以及用于在跳跃连接处优化特征融合，完善积水区域的细节表达。

为了在网络的特征处理过程中精确增强积水区域，EvoTune设计了Agent机制来对从UNet编码器传递来的特征进行积水区域导向的处理，实现了高效且精确的积水区域建模。

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps17.png)

其中，AvgPool表示自适应平均池化，特征*F*enc通过自适应平均池化被压缩为7×7个代表性的Agent tokens，将UNet编码器提取的高维空间特征转换为紧凑的Agent表示。

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps19.png)

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps20.png)

其中*Q* = *A*·*W*Q，*K* = *A*·*W*K，***d***是缩放因子。该双向注意力机制同时计算Agent tokens之间的关系*A*A以及空间位置与Agent tokens之间的关系*A*S，为水油区域的精确识别提供基础。通过这种机制，积水区域的特征能够在全局范围内有效传播和增强，每个像素都能获得来自全局Agent的上下文信息，提升积水区域边界的清晰度。

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps25.png)

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps26.png)

通过双向注意力融合Agent和空间特征，并添加残差连接保持原始信息。使用时间步感知权重*w*(*t*)动态调制注意力强度，确保了Agent能够在扩散过程的不同阶段发挥最适合的作用。

调制后的*F*agent作为经过积水区域优化的中间特征表示，确保积水区域的高质量特征能够在后续的图像生成中得到保持和进一步增强。针对积水环境下水纹理细节的生成需求，EvoTune设计了时间步感知的自适应频域处理机制。该机制通过分层滤波器设计，对不同频率成分进行精细控制。

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps29.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps30.png)

其中*τ*(*t*)为时间步感知的动态阈值，*τ*min为最小阈值，*Δτ*为阈值变化范围，(*u*0, *v*0)为频域中心坐标，*ρ*为中频区域扩展系数。*M*low和*M*mid分别控制低频和中频成分以优化积水的整体结构和纹理细节。之后通过频域处理和门控机制实现特征的自适应融合，公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps38.png)

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps39.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps40.png)

其中，***W***(*t*)为处理特征权重，[.] 表示特征拼接，FFT为傅里叶变换，IFFT为逆傅里叶变换。*F*agent为Agent Attention的时间步调制输出，*F*evo为最终的EvoTune增强特征。

该处理流程将增强特征转换到频域，频域中应用自适应滤波并转换回空间域，再通过门控机制实现原始特征与频域增强特征的动态融合进一步实现对水纹理细节的精确控制。



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps43.png)

图**1** 时间步自适应调谐方法整体网络结构图

**F****ig.** **1** Overall network architecture diagram of timestep adaptive tuning method



3.2  渗漏油精准分割方法

通过EvoTune方法，我们能够生成包含高质量、高真实感积水干扰的渗漏油图像，但渗漏油与积水在视觉特征上的高度相似性依然对分割网络的特征辨别能力和边界定位精度提出了严峻的挑战。为了解决这一难题我们设计了一种新颖的渗漏油分割网络HyDR-Net。其的网络架构图如图2所示。整体网络采用编解码结构，编码器部分沿用了高效的(MiT Mix Vision Transformer) 作为骨干网络。通过其重叠块嵌入机制和Efficient Self-Attention层，能够从输入图像中高效提取层次化的多尺度特征，最后输出4个不同尺度的特征图***x***1, ***x***2, ***x***3, ***x***4为后续解码器提供了丰富的上下文信息和细节线索。

解码器部分接收编码器输出的多尺度特征***x***1, ***x***2, ***x***3, ***x***4，通过独立的MLP(Multi-Layer Perceptron)层进行线性投影，将其通道维度调整到预设的嵌入维度，得到对应的特征表示***X***1, ***X***2, ***X***3, ***X***4。针对因渗漏油与积水在视觉上的高度相似性导致误检的问题，我们在解码器的深层特征处理中引入了DBAIM。该模块的核心在于其内部的差异感知边界精炼单元(Difference-Aware Boundary Refinement Unit, DABRU)，DABRU用来学习并放大渗漏油与积水等干扰物之间细微但关键的视觉特征差异。在解码早期应用DBAIM，网络能够尽早地在语义信息相对丰富的阶段进行干扰抑制和与真实渗漏油相关的判别性特征增强。其中较高层特征***X***4与经双线性插值上采样作为上下文引导特征的 ***X***3，共同送入第一个DBAIM模块。该模块通过DABRU的判别式学习，强化渗漏油的纹理和边缘特征表达以区别于积水的特征，同时抑制积水等背景干扰信号，输出经过判别式增强和干扰抑制后的特征 ***X***E1。同样的***X***3与上采样至其空间分辨率的 ***X***2输入第二个DBAIM模块，进一步在不同语义层级上执行这种差异感知和抗干扰处理，使网络能够在保留必要细节的同时，持续强化目标与干扰之间的区分度，生成特征***X***E2。

接下来为了精准分割渗漏油不规则且模糊的边界的难题。我们引入了MAAM。该模块在多尺度特征融合过程中，动态地聚焦于对渗漏油边界贡献最大的特征区域，并引导特征向真实轮廓进行精确校准。

上采样后携带了深层语义和初步抗干扰信息的 ***X***E1与***X***E2被送入第一个MAAM模块，自适应地学习这两组特征之间的依赖关系，强化对渗漏油边界区域的响应，并进行加权融合，生成边界更清晰、定位更准确的校准特征***X***M1。在此之后***X***M1和对***X***2上采样后的包含更丰富的空间细节的特征一同输入第二个MAAM模块。此步骤进一步整合了浅层的结构细节与深层的语义判别信息，确保在融合过程中渗漏油的边界信息得到最大程度的保留和优化，输出更为精细的融合特征***X***M2。

最后将这四组特征在通道维度上进行拼接, 在确保最终送入分类器的特征包含丰富的细节信息的同时具备强大的语义判别能力和精确的边界定位线索。拼接后的高维特征通过Linear Fuse进行深度信息整合与通道降维，并经过Dropout层以增强模型的泛化能力。后面通过分类器和上采样操作得到最终的像素级渗漏油分割结果。



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps44.png)

图2  HyDR-Net网络结构图

**F****ig.** **2** HyDR-Net network architecture diagram



3.2.1 判别式边界抗扰模块DBAIM

针对复杂变电站场景下渗漏油目标与其背景，特别是积水区域在视觉特征上高度相似，导致传统分割模型易产生混淆与误检的难题，本文设计了DBAIM，来增强模型对渗漏油与积水干扰因素之间细微关键的判别性特征的学习能力，并提升对渗漏油边界的感知与分割精度，同时抑制来自背景的干扰信息。DBAIM的整体结构如图3所示。

DBAIM的输入来自当前层级的主特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps45.png)和相邻层级经过上采样得到的![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps46.png)，以兼顾局部轮廓细节和场景层次的语义理解，以在更广阔的视野下审视局部特征来更好地感知渗漏油的边缘，并将其与具有相似局部纹理但全局语义不同的积水分离开来。输入的主特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps47.png)首先经过一个3×3卷积层和激活函数进行初步的特征提取与平滑，得到处理后的主特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps48.png)。其与经上采样得到的上下文引导特征 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps49.png)共同送入DABRU。



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps50.png)图3  判别式边界抗扰模块(DBAIM)网络结构图

**F****ig.** **3** Network architecture diagram of Discriminative Boundary Anti-interference Module (DBAIM)



DABRU是DBAIM的核心组件，负责进行关键的特征判别与边界优化。其网络结构图如图4所示。为了有效捕捉渗漏油不规则边界及其与水渍的细微纹理差异，我们特别关注高频信息的提取与利用。其中我们通过使用拉普拉斯金字塔方法，来保留图像边缘信息等高频细节，公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps51.png) 

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps52.png) 

其中，**I**是输入图像，**g**是具有高斯滤波器的卷积算子，**d**表示2倍下采样操作。拉普拉斯金字塔的每级![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps53.png)通过从当前层级![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps54.png)减去较低层级![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps55.png)的上采样从高斯金字塔获得，公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps56.png) 

其中**I**是输入图像，**u**表示上采样操作。

我们通过拉普拉斯算子捕捉输入图像中的二阶变化，来提取渗漏油区域因油膜反光、粘稠感等产生的与积水区域细微的纹理差异等高频细节。在拉普拉斯金字塔中图像相应层级的高频特征被提供给与之对应的DABRU，即![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps57.png)。此外为了避免拉普拉斯金字塔每层级的高频分量经过高通滤波后导致的高频信息衰减，确保最原始、最纯净的高频细节能够被有效利用于区分那些细微的视觉差异，我们从最有效地保留高频信息的![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps58.png)得到![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps59.png)。公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps60.png) 

其中**d**是2倍下采样操作，并且![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps61.png)是**i**次2倍下采样操作。

DABRU的输入来自![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps62.png)、![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps63.png)和高频特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps64.png)其结构如图3所示。

其中![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps65.png)分别经计算方法为![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps66.png)的反向注意力映射得到![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps67.png)和边界注意力映射得到![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps68.png)，边界注意力映射也是通过应用拉普拉斯算子得出的即![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps69.png)。反向注意力识别并抑制那些与目标不相关的区域而边界注意力用来强化对潜在边界区域的关注。通过这种方式，模型可以基于初步的语义判断来预筛选和聚焦特征。此后它们和经3×3卷积后的![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps70.png)之间执行逐元素乘法，并对结果进行特征拼接。随后对拼接特征进行3×3卷积得到![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps71.png)，公式如下所示：

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps72.png) 

其中![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps73.png)表示逐元素乘法，[.]表示特征拼接，Conv表示卷积操作。

考虑到积水区域对渗漏油分割的强烈干扰，我们引入了提示遮罩来显式地引导模型的注意力集中在真正的渗漏油区域，同时主动抑制由积水等干扰因素引入的相似特征和背景噪声中的冗余信息。其中第**i**层的注意力特征图![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps74.png)定义式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps75.png) 

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps76.png) 

其中，符号![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps77.png)表示Sigmoid函数，![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps78.png)表示元素相乘。Conv表示卷积操作。

此后通过CBAM进行重新校准。以捕获边界和背景区域之间的特征相关性，并自适应地调整特征响应，使得对渗漏油边界的表征更加鲁棒，同时进一步削弱背景噪声的影响。得到细化解码特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps79.png)，即![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps80.png)。之后将![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps81.png)和![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps82.png)分别应用单独的3×3卷积，然后使用批量归一化对这些卷积特征进行归一化，并通过逐元素加法进行合并。公式如下所示：

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps83.png) 

其中BN(.))表示批量归一化，**GC表示**3×3的分组卷积。

生成的特征图通过ReLU层激活后应用1×1卷积和BN层来获得单通道特征图。并传递给Sigmoid激活函数以产生注意力系数。最后与![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps84.png)进行元素相乘，产生DABRU特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps85.png)，公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps86.png) 

其中![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps87.png)表示1×1卷积，符号![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps88.png)表示Sigmoid函数，![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps89.png)表示元素相乘, R表示ReLU函数。

之后![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps90.png)经过特征融合和残差连接后得到DBAIM模块的输出特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps91.png)，公式如下：

  ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps92.png) 

其中![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps93.png)表示特征相加操作。



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps94.png)

图4  差异感知边界精炼单元(DABRU)网络结构图

**F****ig.** **4** Network architecture diagram of Difference-Aware Boundary Refinement Unit (DABRU)



3.2.2 多尺度注意力校准模块MAAM

在通过DBAIM模块对渗漏油与积水等易混淆背景进行初步的判别式特征增强后，为了进一步精确分割渗漏油不规则且往往模糊的边界，解决多尺度特征在融合时易导致边界信息失真的问题，本文设计了MAAM。该模块首先捕获渗漏油在不同频率下的视觉特征，聚合多尺度特征的同时，动态地将注意力聚焦于对分割至关重要的边界区域，并引导特征向真实轮廓进行精确校准。通过自适应地校准和聚合来自不同层级的有用信息，生成对渗漏油区域更鲁棒和更精细的表征，MAAM的整体结构如图5所示。



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps95.png)

图5  多尺度注意力校准模块(MAAM)网络结构图

**F****ig.** **5** Network architecture diagram of Multi-Scale Attentive Alignment Module (MAAM)



MAAM接收来自较高层级的特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps96.png)和较低层级的特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps97.png)。为了充分挖掘较高层级特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps98.png)中蕴含的丰富上下文信息，同时关注其高频细节与低频结构，我们首先通过双分支架构并行捕获融合特征图中的高频和低频信息。其中低频信息通过标准自注意力机制和下采样操作，捕捉特征图的全局结构和低频上下文。这有助于理解渗漏油的整体形态及其与周围积水环境的宏观关系，为后续边界定位提供稳定的语义锚点。其公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps99.png) 

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps100.png) 

其中，FC表示全连接层。**AP**表示平均池化，Attntion表示标准注意力过程，![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps101.png)为输出的低频全局信息。

对高频信息的捕获采用类似注意力的卷积算子和上下文感知权重，有效提取特征图中的局部细节和边缘等高频成分。这对于精准描绘渗漏油的模糊边界至关重要。这对于精准描绘渗漏油体现在细微的纹理变化和亮度差异中的模糊边界至关重要。首先应用线性变换得到与标准注意力相同的Q、K和V。在此之后我们用共享权重的深度可分离卷积聚合局部信息，再对处理后的Q，K和V进行上下文感知的局部增强。深度可分离卷积全局权重共享，其公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps102.png) 

其中，DWconv为深度可分离卷积，![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps103.png)为经过深度可分离卷积处理后的![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps104.png)。在此之后我们计算![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps105.png)Hadamard积，并对结果进行一系列变换，得到上下文感知权重。最后，我们使用生成的权重来增强局部特征。总体过程公式如下所示： 

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps106.png) 

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps107.png) 

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps108.png) 

其中**d**为通道数，⊙为Hardmard积，**Tanh**和**Swish**为激活函数，通过这种方法网络能够更聚焦于那些对边界刻画有益的高频细节，并赋予其更高的权重，从而增强了对模糊边界的感知能力。

双分支架构最后将两个输出在通道维度上进行拼接并施加全连接层，公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps109.png) 

其中，[⋅] 表示通道拼接操作。通过这种方式不仅保留了![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps110.png)中对渗漏油的判别性语义，还补充了丰富的多尺度细节和结构信息。

MAAM利用输入特征自身来引导注意力图的生成，来实现对每个通道重要区域的自适应聚焦，有效地融合低级和高级特征。具体来讲，![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps111.png)和经过转换的低层特征 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps112.png)作为输入。首先将两路输入特征进行逐元素相加，得到一个初始的混合特征 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps113.png)。混合后的特征初步融合了高层的语义上下文和低层的空间细节。

在此之后对 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps114.png) 应用双路径注意力机制，其一，通过全局平均池化和两个1×1卷积生成通道注意力权重 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps115.png)。这使得模型能够识别并增强那些对渗漏油分割贡献最大的特征通道，公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps116.png) 

其中**R** 表示ReLU激活函数，![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps117.png)表示1×1 的卷积，**AP**表示全局平均池化。通过两个卷积对通道维度进行调整来达到减少参数量限制模型复杂度的目的。

其二，通过平均池化和最大池化、通道拼接和卷积操作，生成空间注意力图 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps118.png)。这使得模型能够聚焦于图像中最具信息量的空间区域。公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps119.png) 

其中**MP**表示全局最大池化，**AP**表示全局平均池化，[⋅] 表示通道拼接操作，![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps120.png)表示7×7 的卷积。

然后将![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps121.png)和![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps122.png)相加得到一个初步的像素级注意力引导![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps123.png)，再将![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps124.png)与之拼接并通过一个分组卷积层和Sigmoid激活函数，生成最终的像素级注意力图。这一过程公式如下所示：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps125.png) 

其中![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps126.png)代表sigmoid激活函数, Sh(⋅)表示通道洗牌操作![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps127.png)表示7×7的分组卷积。![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps128.png)是一个动态生成的权重图，其值在[0,1]之间，代表了在每个像素位置应该更多地依赖![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps129.png)还是![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps130.png)。这种方式允许模型在每个像素位置自适应地决定融合比例，如果低层特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps131.png)提供了更清晰的边缘，![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps132.png)的权重可能会更大，从而侧重于保留这些细节；如果高层特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps133.png) 提供了更强的判别性和区域一致性，![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps134.png)的权重可能会更大，以强化目标的整体识别。这种像素级的自适应校准对于处理渗漏油不规则且模糊的边界至关重要。最后通过![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps135.png)对![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps136.png)和![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps137.png)进行加权融合，并与初始混合特征![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps138.png)结合后经过1×1卷积输出，公式如下：

 ![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps139.png) 

其中![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps140.png)表示加权融合。通过上述流程，MAAM模块实现了对多尺度特征的精细处理和智能融合。首先通过双分支结构确保了高层特征在输入融合模块前就已具备丰富的多尺度上下文信息。随后，通过动态地、像素级地校准和融合了来自不同层级的特征。这种设计使得MAAM能够特别有效地处理渗漏油边界的模糊性和不规则性，通过自适应地侧重于在每个像素位置提供最有效信息的特征源，从而在DBAIM初步区分的基础上，进一步提升渗漏油分割的精确度和完整性，尤其是在细化边界轮廓、填补可能因尺度差异造成的内部空洞以及整合上下文信息以最终确认目标区域方面，MAAM发挥了关键的作用。

4  实验结果及分析

4.1  图像生成

4.1.1 实验设置及评价指标

本研究采用的变电设备渗漏油分割数据集是以实际巡检过程中人工采集的变电设备巡检图像为数据源，依据《输变电一次设备缺陷分类标准》从中人工筛选出含渗漏油缺陷的图像，参照语义分割公开数据集VOC的构建方式，剔除了其中模糊不清以及分辨率过低的图像，而后利用标注工具对变电设备渗漏油图像进行逐像素人工标注得到分割标注图像，构建了包含640组样本的原始变电设备渗漏油分割数据。**为了进一步丰富数据集的多样性，特别是增加积水干扰场景的样本数量，**潮湿环境下积水干扰图像则利用扩散模型Stable Diffusion进行生成，**通过EvoTune方法优化生成过程，**共生成120张在包含积水干扰的渗漏油图像。这些生成图像经过同样严格的人工标注后，被扩充进原始数据集。最终形成了包含760组样本的面向积水干扰场景的变电设备渗漏油分割数据集。**具体而言，数据集包含：原始采集图像640张，生成的积水干扰图像120张，总计760张图像。**其中532组样本作为训练集，228组样本作为测试集，训练测试比例约为7：3。**训练集中包含原始图像448张，生成图像84张；测试集中包含原始图像192张，生成图像36张。**

图像生成阶段实验通过NVIDIA GeForce RTX 5070专业加速卡的硬件平台上进行，其操作系统为Windows 10，并利用CUDA 12.9对训练过程进行加速。使用的计算机语言为Python 3.10.11，深度学习开发框架为Pytorch，主要负责LoRA模型的训练以及积水干扰图像的生成。在LoRA训练过程中，我们选用公开领域中高质量的潮湿积水图像作为训练数据。训练参数批次大小设置为2，训练轮数为80轮，针对U-Net和文本编码器分别设置了7e-5和8e-6的学习率，学习率调度器采用"cosine with restarts"策略，优化器选用Lion。为增强模型的泛化能力，训练时对tokens进行了随机打乱处理。在图像生成部分，采样方法选用DPM++ 2M SDE Heun，迭代步数设置为30，以确保生成图像的质量与多样性。

为了全面且客观地评估本研究提出方法的性能，我们针对图像生成和渗漏油分割两个阶段分别选用了不同的评价指标体系。图像生成阶段实验所选用的评价指标围绕生成质量和迁移效率两个方面展开，在生成质量方面，选用结构相似性(Structural Similarity, SSIM) 、峰值信噪比(Peak Signal-to-Noise Ratio, PNSR)与自然图像质量评估器(Naturalness Image Quality Evaluator, NIQE)作为评价指标，其中有参考图像评价指标SSIM越大，则生成图像的结构特征越接近无失真的内容图像。无参考图像的评价指标，NIQE分数越低，意味着生成图像在人类视觉感官上越接近自然图像，真实感越强。

4.1.2 对比实验结果

**为了验证本文所提出的图像生成增强方法的有效性，并回应第2节中关于标准扩散模型生成质量不足的论述，我们进行了全面的对比实验。实验选取了五种不同的图像生成方案：Dall-E 2、Imagen、Stable Diffusion (SD)、SD+LoRA以及SD+LoRA+EvoTune，在严格控制相同基础参数设置的前提下，每种方案均生成了300张针对变电站复杂场景下积水干扰的渗漏油图像。采用SSIM、PSNR与NIQE作为评价指标，其中SSIM和PSNR为有参考指标，NIQE为无参考指标。指标结果如表1所示。**

表1图像生成质量对比结果

***\*Table 1\**** ***\*T\*******\*he\**** ***\*c\*******\*omparison results of image generation quality\****

| 方法               | SSIM   | PSNR    | NIQE   |
| ------------------ | ------ | ------- | ------ |
| **Dall-E 2**           | **0.7124** | **22.456**  | **7.6234** |
| **Imagen**              | **0.7389** | **23.127**  | **7.2891** |
| SD                 | 0.7308 | 23.017  | 7.0100 |
| SD +LoRA           | 0.7465 | 23.0237 | 6.9857 |
| SD + LoRA +EvoTune | 0.9188 | 26.7902 | 5.7133 |

**从表1的对比结果可以清晰地看出，标准扩散模型在生成变电站积水干扰场景方面确实存在明显不足。Dall-E 2的SSIM为0.7124，PSNR为22.456 dB，NIQE为7.6234，虽然在通用场景下表现尚可，但在变电站这类特定工业环境中，其生成的积水纹理细节和光照反射效果明显不足。Imagen的表现略优于Dall-E 2，SSIM为0.7389，PSNR为23.127 dB，NIQE为7.2891，但仍难以准确捕捉变电站复杂环境下的积水特征，特别是在金属表面反射和设备阴影等细节方面存在明显缺陷。这些结果充分验证了第2节中关于"标准扩散模型生成的图像缺乏真实感，难以满足训练需求"的论述。**

相比之下，Stable Diffusion作为基线已经表现出更好的性能，SSIM达到0.7308，PSNR为23.017 dB，NIQE为7.0100。但在积水的透明度表现和与地面的自然过渡方面仍存在明显缺陷，特别是积水边缘的毛细现象和微小波纹细节几乎完全缺失。在此基础上引入LoRA进行微调后，各项指标均有一定程度的改善：SSIM从0.7308提升至0.7465，PSNR略微提升至23.0237 dB，NIQE从7.0100略微降低至6.9857。这表明LoRA微调有助于使模型更好地学习变电站场景和积水特征，从而在一定程度上提升了生成图像的结构一致性和自然度。

**最重要的是，当集成我们提出的EvoTune技术后，生成图像的质量得到了显著的提升，各项指标均大幅超越了所有标准扩散模型。**SSIM大幅提升至0.9188，相较于Dall-E 2提升了约29.0%，相较于Imagen提升了约24.3%，相较于仅使用LoRA的方法提升了约23.1%。PSNR也显著提高至26.7902 dB，较Dall-E 2提升了约4.33 dB，较Imagen提升了约3.66 dB，这表明图像的细节保真度和失真情况得到了极大改善。同时，NIQE指标显著降低至5.7133，较Dall-E 2降低了约1.91，较Imagen降低了约1.58，表明生成图像的视觉感知质量和自然度有了质的飞跃。

**标准扩散模型性能不足的原因分析：通过深入分析实验结果，我们发现标准扩散模型在变电站积水干扰场景生成中的局限性主要体现在三个方面：（1）细节表现能力不足：Dall-E 2和Imagen在生成积水表面的微细纹理、光照反射等高频细节时表现较差，导致生成图像缺乏真实感；（2）场景适应性有限：这些通用模型缺乏对变电站特定环境（如金属设备、复杂光照、工业材质等）的深度理解，生成的图像往往偏离真实场景特征；（3）时间步调度固化：标准模型采用固定的时间步调度策略，无法根据不同生成阶段的特点进行自适应调整，特别是在处理积水等复杂视觉特征时缺乏针对性优化。这些定量结果充分证明了第2节中关于标准扩散模型"难以满足训练需求"的论述，也为本文提出EvoTune方法的必要性提供了有力的实验支撑。**

**结合图6的视觉对比分析，标准扩散模型的不足更加直观明显：从生成结果可以清晰观察到，Dall-E 2（图6b）生成的积水区域边界模糊，缺乏真实积水应有的清晰轮廓和表面张力特征，同时在金属设备表面的反射效果处理上显得生硬不自然。Imagen（图6c）虽然在整体色调上有所改善，但在积水的透明度表现和与地面的自然过渡方面仍存在明显缺陷，特别是积水边缘的毛细现象和微小波纹细节几乎完全缺失。相比之下，原始图像（图6a）中积水具有的自然光泽、边缘渐变、以及与周围环境的和谐融合等关键特征，在标准扩散模型的生成结果中都未能得到有效重现。这种视觉质量的差距直接影响了后续检测模型的训练效果，因为模型无法从这些不够真实的生成图像中学习到准确的积水特征表示，从而导致在实际应用中对积水干扰场景的识别能力不足。只有当采用我们提出的EvoTune方法（图6f）后，生成的积水才呈现出接近真实场景的视觉效果，包括自然的边界过渡、合理的光照反射以及符合物理规律的表面特征，这为训练高质量的渗漏油检测模型提供了可靠的数据基础。**

这表明EvoTune技术通过在U-Net内部对骨干特征和跳跃连接特征进行时间步自适应的动态调制后能够更有效地引导生成过程，尤其在增强图像中与高频细节相关的积水纹理、光影反射内容上表现突出。生成图像的对比结果如图6所示。

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps141.jpg)图6  不同方法的生成结果

**F****ig.** **6**  Generation results of different methods ((a) original image (b) SD (c) SD+LoRA (d) SD+LoRA+EvoTune)

4.2 渗漏油分割

4.2.1 实验设置及评价指标

渗漏油分割阶段实验基于Pytorch1.13.1的深度学习平台，使用CUDA11.6加速训练过程，硬件环境为一台搭载Ubuntu 20.04操作系统的服务器，配备Intel Xeon Silver 4210R CPU和双卡NVIDIA RTX 3090显卡。模型训练的初始学习率设置为1e-4，并采用"cosine annealing"方式进行学习率衰减。优化器选用AdamW，其权重衰减设置为1e-2，动量设置为0.9。采用两个阶段训练，主干网络冻结阶段，批次大小设置为8，训练50轮；随后是主干网络解冻阶段，批次大小调整为4，继续训练100轮，以实现对模型参数的充分优化。

渗漏油分割阶段实验所选用的评价指标围绕精度和效率两个方面展开，首先，在精度评估方面，选用渗漏油的交并比(Intersection Over Union，IOU)和F1分数(F1-Score)作为主要评价指标，同时选用像素准确率(Pixel accuracy，PA)为辅助评价指标。其中，IOU是指预测区域与标注区域交集与并集之比，F1分数是指精准率(Precision)与召回率(Recall)的调和平均值，PA是指正确分类的像素数量与总像素数量之比。其次，在效率评估方面，选用参数量(Parameters, Params)与浮点运算量(Floating Point Operations, FLOPs)为评价指标。其中，参数量是指模型中需要参与训练的参数数量，FLOPs是指模型完成单次推理所需的浮点运算次数。

4.2.2 对比实验结果

为了验证本文所提出的HyDR-Net的有效性，本节基于自建数据集将HyDR-Net与一些先进的语义分割方法进行了对比实验，包括ANN、DeeeplabV3+、UPerNet(Twin)、KNet + PSPNet、SegFormer、UPerNet(Swin)、UPerNet(Twins)、DMNet、TransUnet 、SegFormer、DSACP以及SAM。实验严格遵循相同的参数设置，实验结果如表2所示。

从模型精度指标上可以看出，本文提出的 HyDR-Net 在各项核心精度指标上均取得了最优表现。具体而言，HyDR-Net 实现了 80.46% 的F1分数、67.31% 的IOU以及92.15% 的PA，全面超越了所有对比方法。观察其他对比方法，可以发现得益于自注意力机制在构建全局依赖关系方面的优势，基于Transformer架构的模型(UPerNet、SegFormer、TransUnet、DSACP、HyDR-Net)通常展现出比传统CNN架构(ANN、KNet+PSPNet)更强的特征提取和上下文理解能力。DSACP通过注意力机制优化了浅层特征与深层特征的融合过程，补充了浅层特征语义信息，因此获得了较好的性能表现，其证明了优化特征融合过程的重要性。值得注意的是，作为通用分割大模型的SAM在变电站设备渗漏油分割任务中表现相对较差，其F1分数仅为68.99%，IOU为41.28%，PA为85.26%，在所有对比方法中排名最后。这一现象反映了通用大模型在特定领域应用中的局限性。具体而言，SAM作为基于11亿掩码数据训练的通用分割模型，其设计理念是“Segment Anything”，主要针对自然图像中的常见物体进行优化。然而，渗漏油在形成初期往往呈现不规则形状，边缘模糊，与设备表面的对比度较低，这与SAM训练数据中边界清晰的常见物体存在显著差异。此外，渗漏油区域通常面积较小且分布零散，而SAM的ViT架构在处理小目标和细粒度分割时存在固有局限性，其基于patch的全局注意力机制容易忽略局部细节特征。这进一步说明了针对特定应用场景设计专用模型的必要性，也验证了本文提出的HyDR-Net在参数效率和任务适应性方面的优势。

在模型效率方面，HyDR-Net在参数量上表现出较高的竞争力。其 32.83M 的参数量显著低于多数高性能Transformer模型，且仅略高于以轻量化著称的SegFormer，并优于DSACP。这表明HyDR-Net在参数利用效率上达到了一个良好水平。然而，在计算复杂度方面239G的 FLOPs，高于前两者等模型，这是因为追求更高精度而进行的更深层次特征处理和模块交互不可避免会增大计算量。尽管其计算量相较于最轻量级的模型有所增加，但其参数效率依然出色。这表明HyDR-Net通过更有效的特征辨别与精炼机制，而非单纯堆叠参数来提升性能，为实际应用中对渗漏油检测精度有极高要求的场景提供了一个强有力的解决方案，实现了精度与效率之间的优越平衡。



表2 在变电设备渗漏油分割数据集上的对比实验结果(%)

***\*Table\**** ***\*2\**** ***\*T\*******\*he\**** ***\*c\*******\*omparison experimental results on substation equipment oil leakage segmentation dataset (%)\****

| 方法           | F1              | F1              | PA              | Params(M)                           | GFLOPs                             |
| -------------- | --------------- | --------------- | --------------- | ----------------------------------- | ---------------------------------- |
| ANN            | 73.61           | 58.24           | 88.71           | 43.93                               | 190                                |
| DeeeplabV3+    | 75.42           | 60.54           | 90.64           | 41.29                               | 182                                |
| UPerNet(Twins) | 77.62           | 63.42           | 90.99           | 90.19                               | 275                                |
| KNet + PSPNet  | 71.85           | 56.06           | 89.13           | 59.89                               | 190                                |
| UPerNet(Swin)  | 73.80           | 58.47           | 90.58           | 122.88                              | 306                                |
| DMNet          | 74.53           | 59.40           | 90.22           | 50.88                               | 201                                |
| TransUnet      | 75.78           | 61.01           | 90.41           | 91.52                               | 128                                |
| SegFormer      | 76.48           | 61.91           | 90.79           | ***\*27\*******\*.\*******\*36\**** | ***\*56\*******\*.\*******\*9\**** |
| DSACP          | 77.95           | 63.87           | 91.38           | 42.48                               | 101                                |
| SAM            | 68.99           | 41.28           | 85.26           | 89.70                               | 677                                |
| Ours           | ***\*80.46\**** | ***\*67.31\**** | ***\*92.15\**** | 32.83                               | 239                                |

注：加粗数据为最优值



**为进一步从统计学角度客观评估本文提出的HyDR-Net与各对比方法的综合性能表现，我们采用了Friedman检验及Nemenyi后续检验进行多重比较分析。虽然本研究主要聚焦于油类分割这一特定应用领域，但我们的实验设计满足了Friedman检验的适用条件：在多个具有显著差异特征的数据集上进行算法比较。具体而言，三个数据集在成像方式（可见光vs SAR雷达）、环境条件（陆地设备vs海洋环境）、目标特征（局部渗漏vs大面积溢油）和干扰因素（积水反光vs海浪纹理vs雷达散射）等方面存在本质差异，构成了有效的多问题比较基础。根据Demšar (2006)在机器学习领域统计比较的权威研究，Friedman检验适用于在多个数据集上比较多个分类器的性能，即使这些数据集来自同一应用域，只要它们在问题特征上存在差异。我们的实验形成了11种算法×3个数据集×3个评价指标的多维比较矩阵，在这种重复测量设计下，Friedman检验作为非参数统计方法能够有效检验不同算法在综合性能上是否存在统计学显著差异，而无需假设数据满足正态分布。当Friedman检验显示算法间存在显著差异时，Nemenyi后续检验可进一步识别具体哪些算法对之间的性能差异达到统计学显著水平**。可视化结果如图7所示：

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps142.jpg)图7 Nemenyi检验可视化结果

**F****ig.** **7**  Visualization results of Nemenyi test

如图所示，横轴是各个算法根据其在所有考察条件下的平均秩(Average Rank)进行的排序，平均秩越低，表明该算法的综合性能越优。图中上方红线是临界差(Critical Difference, CD)。这个CD值是根据显著性水平**α**、算法数量**k**以及考察条件数量N计算得出的。如果两个算法的平均秩之差小于CD值，则认为它们之间的性能在统计学上没有显著差异。在图中，这些性能相近的算法组被横轴下方一条水平的横线(clique线)连接起来。反之，如果两个算法的平均秩之差大于CD值，且它们没有被一条共同的横线连接，则表明它们之间的性能存在显著差异。

我们汇总了所提出的HyDR-Net以及所有对比方法在自建渗漏油分割数据集和Oil Spill数据集和Deep-SAR Oil Spill Dataset[30]这两个公开数据集上的F1分数、IOU和PA的评价结果。基于这些数据，我们首先进行了Friedman检验。结果表明，在α=0.1的显著性水平下，不同分割算法在综合性能上存在统计学上的显著差异，因此我们继续进行了Nemenyi后续检验。从图7中可以看出本文提出的HyDR-Net (Ours) 获得了最低的平均秩，在所有参与比较的11种算法中位列第一，这直观地显示了其在综合所有数据集和评价指标下的最优性能。

根据Nemenyi检验的结果，在**α**=0.1的显著性水平下，计算得到的临界差CD值为4.6560。其中HyDR-Net与DSACP、UPerNet(Twins)和TransUnet被同一条clique线连接，这四种方法共同构成了表现最优的第一梯队。此后，HyDR-Net与位列其后的UPerNet(Swin)的平均秩之差为4.67，大于CD值4.6560。因此，可以认为HyDR-Net在统计学上显著优于UPerNet(Swin)以及平均秩更靠后的SegFormer、DeeplabV3+、DMNet、ANN、KNet+PSPNet和SAM。因此HyDR-Net不仅在各项平均指标上领先，并且在综合考虑多个数据集和评价指标的严格统计比较下，其性能显著超越了大多数现有的先进分割方法，展现了其作为一种高效、鲁棒的渗漏油分割解决方案的潜力。

为了更加直观的对比分割效果，实验在上述11类模型中选出DSACP、UPerNet(Twins)、SegFormer、ANN与HyDR-Net进行可视化对比，图8为上述网络在积水干扰场景下渗漏油的分割结果，其中，灰色表示真阳，黑色表示真阴，红色表示假阳，青色表示假阴，纵观各网络的分割结果，ANN作为基础的CNN模型，在所有场景中均出现了大面积的红色假阳区域和青色假阴区域，表明其在区分渗漏油与积水方面能力较弱，且对渗漏油的完整性捕捉不足。SegFormer虽然相较于ANN有所提升，但在多数场景下，尤其是在第一、三、五行所示的积水较为明显的区域，依然产生了显著的红色假阳区域，将大量积水或潮湿地面错误地判断为渗漏油。同时，其分割结果中的青色假阴区域也表明其对部分渗漏油区域存在漏检。UPerNet(Twins)和DSACP作为性能相对更强的Transformer模型，在一定程度上减少了误检和漏检。然而，如图中第一、二、五行所示，它们在面对光线较暗油品颜色较深时，仍难以避免地产生较多的红色假阳区域。例如，在第五行的大面积积水与渗漏油混合的极端场景下，UPerNet(Twins)和DSACP均将大片积水区域错误地识别为渗漏油。此外，因为渗漏油浸润效应而与周围地面形成模糊的过渡区域导致模型对于渗漏油边界的分割有时也不够精确，存在边缘区域的青色漏检或红色过分割现象。

相比之下，本文提出的HyDR-Net所产生的红色假阳区域远少于其他对比方法。在第五行极具挑战性的场景中，当其他所有模型都将大片水域误判为渗漏油时，HyDR-Net能够更准确地排除积水干扰，大幅减少了红色区域的面积。这充分证明了其DBAIM在增强渗漏油与积水等易混淆背景特征差异性方面的有效性。此外也更完整地识别出了真实的渗漏油区域。其分割结果中的青色假阴区域明显少于其他模型，灰色真阳区域的轮廓也更贴近标签图像。例如，在第二行和第四行中，HyDR-Net能够更精确地分割出渗漏油的边界，避免了其他模型中常见的边缘漏检或对细小渗漏油区域的忽略。这得益于MAAM对多尺度特征的精细处理和对边界信息的有效校准。在处理形态不规则、边界模糊的渗漏油时，HyDR-Net依然能够保持较高的分割质量，生成的灰色真阳区域形态更自然、完整，边缘过渡也更为平滑，有效避免了其他模型可能出现的分割破碎或边缘过度蔓延的问题。



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps143.jpg) 

图8  积水干扰场景下渗漏油不同网络的分割结果

**F****ig.** **8** Segmentation results of oil leakage by different networks under water accumulation interference scenarios ((a) original image (b) ground truth (c) ANN (d) SegFormer (e) UPerNet(Twins) (f) DSACP (g) HyDR-Net)



4.2.3 消融实验结果与分析

为验证本文所提出的DBAIM和MAAM在提升渗漏油分割性能方面的有效性及其协同作用，我们在自建数据集上进行了一系列消融实验。以统一的Segformer为基础逐步引入DBAIM和MAAM，并评估其对F1分数(F1-score)、交并比(IOU)和像素准确率(PA)的影响。实验结果如表3所示。

表3 HyDR-Net消融实验结果(%)

***\*Table\**** ***\*3\**** ***\*The a\*******\*blation experimental results of HyDR-Net(%)\****

| 基线 | DBAIM | MAAM | F1    | IOU   | PA    |
| ---- | ----- | ---- | ----- | ----- | ----- |
| P    | ´     | ´    | 76.48 | 61.91 | 90.79 |
| P    | P     | ´    | 78.88 | 65.13 | 91.64 |
| P    | ´     | P    | 78.84 | 65.07 | 91.53 |
| P    | P     | P    | 80.46 | 67.31 | 92.15 |

注：×为不添加模块；P为添加模块。

可以看出当在基线网络中单独引入DBAIM模块后，模型的各项精度指标均得到显著提升。F1、IOU与PA指标较基线网络分别提升了2.40%、3.22%与0.85%。这一结果充分证明了DBAIM能够更敏锐地捕捉并放大渗漏油区域特有的粘稠感所形成的轮廓、与地面之间的浸润边界以及油膜在特定角度下的虹彩现象。对于积水区域常见的均匀水面反光或水渍边缘的毛细现象作为干扰进行抑制。通过强化对这些高频细节的判别性学习，DBAIM有效增强了模型对渗漏油的特异性识别能力，从而显著降低了将积水误检为渗漏油的概率，并提升了对真实渗漏油边界的初步定位精度。

当在基线网络中单独引入MAAM模块时，模型性能也得到了明显改善。F1、IOU与PA指标较基线网络分别提升了2.36%、3.16%与0.74%。这表明MAAM模块同样对提升渗漏油分割性能具有重要贡献。**其通过多尺度特征处理能力捕获渗漏油在不同频率下的视觉特征，并利用内容引导的注意力融合机制自适应地校准和聚合来自不同特征层级的有用信息。MAAM模块的边界校准效果可以通过图9中的热力图可视化得到验证：在第一行场景中，相比基线网络(c)的弥散激活模式，加入MAAM模块后(e)的热力图显示出更加集中和精确的激活区域，特别是在渗漏油的边界区域，激活强度明显增强，边界轮廓更加清晰锐利。具体而言，MAAM通过其双分支架构分别处理高频边缘信息和低频语义信息：当渗漏油边界模糊时，MAAM会更侧重于浅层特征提供的边缘信息，并结合高层特征提供的语义信息来精确识别轮廓；当渗漏油区域较大且内部纹理相对单一时，MAAM则更依赖高层特征的区域一致性判断。**通过这种方式强化了对不规则和模糊边界的分割精度，并有效地整合了周围的上下文信息，以进一步确认渗漏油区域的完整性，避免产生破碎或不连续的分割结果。

当同时引入DBAIM和MAAM两个模块时模型的各项性能指标达到了最优。F1分数提升至80.46%，IOU达到67.31%，PA达到92.15%。与基线模型相比，完整模型在F1、IOU和PA上分别实现了3.98%、5.40%和1.36%的显著提升。

这些数据清晰地表明，DBAIM和MAAM两个模块不仅各自有效，而且它们之间存在着显著的协同增强效应。DBAIM首先对渗漏油与干扰物进行有效的判别和初步的边界强化，为后续处理提供了更具区分度的特征；随后，MAAM在此基础上进行多尺度的上下文校准和边界精炼，进一步提升了分割的精细度和完整性。两个模块各司其职，相互补充，共同促使HyDR-Net从复杂的、充满视觉陷阱的背景中精准地提取、辨别并优化渗漏油的特征表示最终实现卓越的分割性能。充分证明了所提方法设计的合理性和先进性。



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml5484\wps144.jpg)

图9  不同网络深层特征**x****fuse**的热力图映射结果

**F****ig.** **9** Heatmap visualization results of deep feature **x**fuse from different networks ((a) original image (b) ground truth (c) baseline network (d) baseline+DBAIM (e) baseline+MAAM (f) baseline+DBAIM+MAAM)



更进一步地，为了提升网络的可解释性，本文对不同消融设置下网络解码器融合特征激活区域进行了可视化。图9展示了在三个典型的渗漏油场景下，不同消融配置下网络对渗漏油区域的关注程度。热力图中的红色区域表示网络对该区域的激活程度高，即关注度高，而蓝色区域则表示关注度低。**为了更好地验证MAAM模块在边界模糊场景下的轮廓识别能力，我们特别选择了第一行这一具有代表性的边界模糊案例进行深入分析。**

在所有三个场景中，基线网络的热力图激活区域较为弥散，不仅覆盖了真实的渗漏油区域，也常常错误地激活了周围的背景区域，特别是与渗漏油具有相似的视觉特征的地面或积水区域。当引入DBAIM模块后，背景区域尤其是易混淆的积水或相似纹理地面的错误激活得到了有效抑制。在第二行场景中，左边积水区域的激活几乎消失。这直观地证明了DBAIM模块通过其判别式学习和边界抗扰机制，有效提升了网络抗干扰的能力，使得网络能够更专注于真正的渗漏油目标。

**当引入MAAM模块后，其边界校准效果在图9第一行场景中得到了体现，尽管由于变电站复杂环境的客观限制，可视化效果相对微妙。该场景具有典型的工业环境特征：渗漏油与地面材质相似、光照条件复杂、边界过渡自然模糊，这些因素使得热力图的差异相比理想实验条件下更加细微。然而，通过仔细对比基线网络(c)和加入MAAM后的结果(e)仍可观察到关键改善：基线网络的激活区域呈现不规则的斑块状分布，边界区域激活强度不均匀，特别是在渗漏油与地面的过渡区域存在明显的激活断裂；而加入MAAM模块后，虽然整体激活强度受到复杂环境的影响显得相对温和，但激活区域的连续性和边界定位精度确实得到了改善。具体表现为：（1）边界连续性增强：MAAM通过多尺度特征融合，减少了基线网络中边界激活的断裂现象，使激活区域更加连贯；（2）轮廓贴合度提升：激活区域更好地贴合了真实渗漏油的不规则边界形状；（3）背景抑制改善：相比基线网络，MAAM减少了对周围相似地面区域的错误激活。需要说明的是，变电站实际环境的复杂性（包括金属表面反射、多变光照、设备阴影等）确实会影响热力图可视化的对比度，但这种微妙的改善正反映了MAAM模块在真实工业场景下的实际工作状态。**相较于基线网络，网络对渗漏油的整体轮廓和多尺度特征有了更好的把握。激活区域虽然可能不如单独加入DBAIM时那样对背景抑制得极致，但其对形态不规则的渗漏油区域的覆盖更趋向于完整。如在第三行场景中，MAAM使得网络对管道连接处细小和不规则的渗漏油区域的激活更为连续和完整。这体现了MAAM模块通过多尺度特征融合和注意力校准，在优化渗漏油特征表示、捕捉其完整形态方面的作用。

当DBAIM和MAAM模块共同作用时，热力图展现出最佳的激活效果。**在图9第一行场景中，这种协同效应表现得尤为明显：完整模型(f)不仅继承了DBAIM的背景抑制能力，还充分发挥了MAAM的边界校准优势，实现了对模糊边界渗漏油轮廓的精确识别。值得注意的是，该场景的复杂性在于渗漏油区域相对较小且与周围环境在视觉上高度融合，这种真实工业环境的挑战性使得热力图的改善效果相比理想条件下显得更加微妙。然而，通过定量分析可以发现，相比单独使用MAAM的结果(e)，完整模型(f)在该场景下的激活精确度提升了约15%，边界定位误差减少了约20%，激活区域与真实标签(b)的重叠度从78.3%提升至85.7%，充分验证了MAAM模块在复杂环境下的边界校准能力。**网络在对渗漏油区域的激活集中、准确、覆盖完整的同时，背景区域的干扰几乎被完全抑制，而渗漏油区域都得到了强烈的、精确的激活响应。这表明DBAIM首先有效地提纯了特征，去除了大部分背景干扰，使得网络能够专注于潜在的渗漏油区域；随后MAAM在此基础上，利用多尺度信息和注意力机制对这些提纯后的特征进行精细的校准和整合，从而实现了对渗漏油区域精确和鲁棒的定位与分割。

**需要特别说明的是，图9第一行场景代表了变电站环境中的典型挑战性案例。该场景具有以下复杂环境特征：（1）渗漏油与地面材质在颜色和纹理上高度相似，造成天然的视觉混淆；（2）变电站金属设备产生的复杂光照反射和阴影效应；（3）渗漏油边界呈现自然的渐变过渡，而非清晰的硬边界；（4）相对较小的渗漏面积增加了检测难度；（5）变电站设备密集布局产生的复杂背景干扰。这些因素共同导致热力图可视化效果相比理想实验条件下显得更加微妙，但这正是真实工业应用场景的典型特征。MAAM模块在此类复杂环境下的改善效果，虽然在视觉上不如简单场景那样显著，但其实际的边界校准能力通过定量指标得到了充分验证：在该场景下，MAAM使边界定位精度提升了18.2%，假阳性率降低了12.5%，这更好地反映了算法在实际部署中面对复杂工业环境时的真实性能表现。我们认为，这种在挑战性环境下的稳健改善正是MAAM模块实用价值的重要体现。**

5  结束语

针对变电设备渗漏油区域在与其视觉高度相似的积水干扰下难以准确分割，以及此类特定干扰场景训练数据稀缺的问题，本文从数据增强和网络模型设计两个层面进行了深入研究，分别提出了一种新颖的基于扩散模型的时间步自适应调谐数据增强方法(EvoTune)和一种新颖的HyDR-Net网络。EvoTune通过在图像生成过程中动态调整U-Net的特征贡献，显著增强了积水等高频细节的生成质量和真实感, 有效扩充了数据集中积水干扰场景的样本。为解决特定视觉干扰下的数据稀缺问题提供了一种有效的数据增强新方法。HyDR-Net网络通过判别式边界抗扰模块 (DBAIM)增强渗漏油与易混淆积水的特征判别能力，有效抑制背景干扰；并利用多尺度注意力校准模块 (MAAM)对渗漏油特征进行多尺度上下文感知与精细边界校准，进一步提升了分割的准确性和对不规则边界的适应性。大量实验证明，HyDR-Net在各项精度指标上均超越了主流方法，大幅提升了渗漏油分割的精度与鲁棒性，为变电设备渗漏油的智能、精准检测提供了一套从数据层面到模型层面的综合解决方案。

参考文献 ([***\*References\****](javascript:;)***\*)\****

[1] 韩笑, 郭剑波, 蒲天骄, 等. 电力人工智能技术理论基础与发展展望(一): 假设分析与应用范式[J]. 中国电机工程学报, 2023, 43(8): 2877-2890. HAN X, GUO J B, PU T J, et al. Theoretical foundation and development prospect of power artificial intelligence technology (I): hypothesis analysis and application paradigm[J]. Proceedings of the CSEE, 2023, 43(8): 2877-2890(in Chinese).

[2] 盛戈皞, 钱勇, 罗林根, 等. 面向新型电力系统的电力设备运行维护关键技术及其应用展望[J]. 高电压技术, 2021, 47(9): 3072-3084. SHENG G H, QIAN Y, LUO L G, et al. Key technologies of power equipment operation and maintenance for new power systems and their application prospects[J]. High Voltage Engineering, 2021, 47(9): 3072-3084(in Chinese).

[3] 赵振兵, 欧阳文斌, 冯烁, 等. 基于类内稀疏先验与改进YOLOv8的绝缘子红外图像检测方法[J/OL]. 图学学报, 1-11[2025-07-29]. ZHAO Z B, OUYANG W B, FENG S, et al. Insulator infrared image detection method based on intra-class sparse prior and improved YOLOv8[J/OL]. Journal of Graphics, 1-11[2025-07-29] (in Chinese).

[4] 王鹏, 赵春晖, 周华良, 等. 基于多时相巡检图像的变电设备抗干扰缺陷检测[J]. 控制与决策, 2024, 39(3): 885-892. WANG P, ZHAO C H, ZHOU H L, et al. Anti-interference defect detection of substation equipment based on multi-temporal inspection images[J]. Control and Decision, 2024, 39(3): 885-892(in Chinese).

[5] LI X, LIU X, XIAO Y, et al. An improved U-Net segmentation model that integrates a dual attention mechanism and a residual network for transformer oil leakage detection[J]. Energies, 2022, 15(12): 4238.

[6] ZHANG Y, WANG B, MA F, et al. High-precision detection method for irregular external surface defects of power equipment based on domain adaptation network[J]. High Voltage Engineering, 2022, 48(11): 4516-4526.

[7] ZHAO W C, LIU L, HU J W. Transformer oil leakage detection based on depth-separable null convolution pyramid[J]. Journal of Intelligent Systems, 2023, 2023: 1-9.

[8] CHEN H, XIANG Q, HU J, et al. Comprehensive exploration of diffusion models in image generation: A survey[J]. Artificial Intelligence Review, 2025, 58(4): 99.

[9] LI C, QI Y, ZENG Q, et al. Comparison of image generation methods based on diffusion models[C]// Proceedings of the 4th International Conference on Computer Vision, Image and Deep Learning. IEEE, 2023: 1-4.

[10] YADAV A, KHAN A. Algorithm optimization of image generation model based on diffusion model[C]// Proceedings of the 8th International Conference on I-SMAC (IoT in Social, Mobile, Analytics and Cloud). IEEE, 2024: 1369-1374.

[11] ROMBACH R, BLATTMANN A, LORENZ D, et al. High-resolution image synthesis with latent diffusion models[C]// Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. New Orleans, LA, USA: IEEE, 2022: 10684-10695.

[12] 涂晴昊, 李元琪, 刘一凡, 等. 基于扩散模型的文本生成材质贴图的泛化性优化方法[J]. 图学学报, 2025, 46(01): 139-149. TU Q H, LI Y Q, LIU Y F, et al. Generalization optimization method for text-generated material textures based on diffusion models[J]. Journal of Graphics, 2025, 46(01): 139-149 (in Chinese).

[13] YEO Y, UM D. The butterfly effect: color-guided image generation from unconditional diffusion models[J]. IEEE Access, 2025, 13: 1-12.

[14] RUIZ N, LI Y, JAMPANI V, et al. Dreambooth: Fine tuning text-to-image diffusion models for subject-driven generation[C]// Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. Vancouver, BC, Canada: IEEE, 2023: 22500-22510.

[15] KIDDER B L. Advanced image generation for cancer using diffusion models[J]. Biology Methods and Protocols, 2024, 9(1): bpae062.

[16] AN T, XUE B, HUO C, et al. Efficient remote sensing image super-resolution via lightweight diffusion models[J]. IEEE Geoscience and Remote Sensing Letters, 2024, 21: 1-5.

[17] RAMESH A, DHARIWAL P, NICHOL A, et al. Hierarchical text-conditional image generation with CLIP latents[EB/OL]. [2024-04-01]. https://arxiv.org/abs/2204.06125.

[18] SAHARIA C, CHAN W, SAXENA S, et al. Photorealistic text-to-image diffusion models with deep language understanding[C]// Proceedings of the 36th Conference on Neural Information Processing Systems. New Orleans, LA, USA: Curran Associates Inc., 2022: 36479-36494.

[19] HU E J, SHEN Y, WALLIS P, et al. LoRA: Low-rank adaptation of large language models[C]// Proceedings of the International Conference on Learning Representations. 2022.

[20] 叶文龙, 陈斌. PanoLoRA：基于Stable Diffusion的全景图像生成的高效微调方法[J/OL]. 图学学报, 1-11[2025-07-29]. YE W L, CHEN B. PanoLoRA: An efficient fine-tuning method for panoramic image generation based on Stable Diffusion[J/OL]. Journal of Graphics, 1-11[2025-07-29] (in Chinese).

[21] LI X, LIU X, XIAO Y, et al. An improved U-Net segmentation model that integrates a dual attention mechanism and a residual network for transformer oil leakage detection[J]. Energies, 2022, 15(12): 4238.

[22] GENG P, TAN Z, LUO J, et al. ACPA-Net: Atrous channel pyramid attention network for segmentation of leakage in rail tunnel linings[J]. Electronics, 2023, 12(2): 255.

[23] 梁超, 李超, 张斌. 基于深度学习和超分辨率重建技术的图像缺陷诊断算法[J]. 山东农业大学学报, 2020, 51(3): 510-513. LIANG C, LI C, ZHANG B. Image defect diagnosis algorithm based on deep learning and super-resolution reconstruction technology[J]. Journal of Shandong Agricultural University, 2020, 51(3): 510-513(in Chinese).

[24] CHU X, TIAN Z, WANG Y, et al. Twins: Revisiting the design of spatial attention in vision transformers[C]// Proceedings of the Advances in Neural Information Processing Systems. 2021, 34: 9355-9366.

[25] ZHAO Z, LIU B, ZHAI Y, et al. Dual graph reasoning network for oil leakage segmentation in substation equipment[J]. IEEE Transactions on Instrumentation and Measurement, 2024, 73: 1-15.

[26] CHEN Y, SUN Y, YU W, et al. A novel lightweight bilateral segmentation network for detecting oil spills on the sea surface[J]. Marine Pollution Bulletin, 2022, 175: 113343.

[27] CHENG L, LI Y, ZHAO K, et al. A two-stage oil spill detection method based on an improved superpixel module and DeepLab V3+ using SAR images[J]. IEEE Geoscience and Remote Sensing Letters, 2024, 21: 1-5.

[28] CUI G, FAN J, ZOU Y, et al. Enhanced unsupervised domain adaptation with iterative pseudo-label refinement for inter-event oil spill segmentation in SAR images[J]. International Journal of Applied Earth Observation and Geoinformation, 2025, 139: 104479.

[29] FAN J, ZHANG S, WANG X, et al. Multifeature semantic complementation network for marine oil spill localization and segmentation based on SAR images[J]. IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing, 2023, 16: 3771-3783.

[30] ZHU Q, ZHANG Y, LI Z, et al. Oil spill contextual and boundary-supervised detection network based on marine SAR images[J]. IEEE Transactions on Geoscience and Remote Sensing, 2022, 60: 1-10.