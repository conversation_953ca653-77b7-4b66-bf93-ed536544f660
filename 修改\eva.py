import os
import numpy as np
import pandas as pd
from PIL import Image
from tqdm import tqdm
from skimage.metrics import structural_similarity as ssim
import cv2
from scipy import ndimage
import warnings
warnings.filterwarnings('ignore')

class ImageEvaluator:
    def __init__(self, original_dir, generated_dir, output_path='evaluation_results.csv'):
        """
        图像质量评估器

        Args:
            original_dir: 原始图片目录路径
            generated_dir: 生成图片目录路径
            output_path: 结果输出文件路径
        """
        self.original_dir = original_dir
        self.generated_dir = generated_dir
        self.output_path = output_path
    
    def check_and_resize_images(self):
        """
        检测图片尺寸一致性，如果不一致则调整生成图片尺寸
        """
        print("正在检查图片尺寸一致性...")
        original_files = sorted([f for f in os.listdir(self.original_dir)
                               if f.lower().endswith(('.png', '.jpg', '.jpeg'))])

        resized_count = 0
        for filename in tqdm(original_files, desc="检查尺寸"):
            original_path = os.path.join(self.original_dir, filename)
            generated_path = os.path.join(self.generated_dir, filename)

            if not os.path.exists(generated_path):
                print(f"警告: 未找到对应的生成图片 {filename}")
                continue

            try:
                # 读取原始图片尺寸
                orig_img = Image.open(original_path)
                orig_size = orig_img.size  # (width, height)
                orig_mode = orig_img.mode

                # 读取生成图片尺寸
                gen_img = Image.open(generated_path)
                gen_size = gen_img.size
                gen_mode = gen_img.mode

                # 检查尺寸是否一致
                if orig_size != gen_size or orig_mode != gen_mode:
                    print(f"调整图片尺寸: {filename} 从 {gen_size}({gen_mode}) 到 {orig_size}({orig_mode})")

                    # 调整生成图片尺寸和模式
                    if gen_mode != orig_mode:
                        gen_img = gen_img.convert(orig_mode)

                    gen_img_resized = gen_img.resize(orig_size, Image.Resampling.LANCZOS)
                    gen_img_resized.save(generated_path)
                    resized_count += 1

            except Exception as e:
                print(f"处理图片 {filename} 时出错: {str(e)}")

        print(f"完成尺寸检查，共调整了 {resized_count} 张图片")

    def calculate_psnr(self, img1, img2):
        """
        计算峰值信噪比 (PSNR)

        Args:
            img1, img2: 输入图像数组
        Returns:
            float: PSNR值
        """
        mse = np.mean((img1.astype(np.float64) - img2.astype(np.float64)) ** 2)
        if mse == 0:
            return float('inf')
        max_pixel = 255.0
        return 20 * np.log10(max_pixel / np.sqrt(mse))

    def calculate_ssim(self, img1, img2):
        """
        计算结构相似性指数 (SSIM)

        Args:
            img1, img2: 输入图像数组
        Returns:
            float: SSIM值
        """
        # 确保图像是灰度图或RGB图
        if len(img1.shape) == 3:
            return ssim(img1, img2, channel_axis=2, data_range=255)
        else:
            return ssim(img1, img2, data_range=255)

    def calculate_niqe(self, img):
        """
        计算自然图像质量评估器 (NIQE)
        这是一个简化版本的NIQE实现

        Args:
            img: 输入图像数组
        Returns:
            float: NIQE分数 (越低越好)
        """
        try:
            # 转换为灰度图
            if len(img.shape) == 3:
                gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
            else:
                gray = img.copy()

            # 计算局部方差
            mu = cv2.GaussianBlur(gray.astype(np.float64), (7, 7), 1.166)
            mu_sq = mu * mu
            sigma = cv2.GaussianBlur(gray.astype(np.float64) * gray.astype(np.float64), (7, 7), 1.166)
            sigma = np.sqrt(np.abs(sigma - mu_sq))

            # 计算结构化系数
            structdis = (gray.astype(np.float64) - mu) / (sigma + 1)

            # 计算特征
            feat = []
            feat.append(np.mean(structdis))
            feat.append(np.var(structdis))
            feat.append(np.mean(np.abs(structdis)))
            feat.append(np.mean(structdis**2))

            # 简化的NIQE分数计算
            niqe_score = np.sqrt(np.sum(np.array(feat)**2))

            return niqe_score

        except Exception as e:
            print(f"计算NIQE时出错: {str(e)}")
            return float('nan')
    
    def evaluate_pair(self, original_path, generated_path):
        """
        评估单对图像的质量指标

        Args:
            original_path: 原始图片路径
            generated_path: 生成图片路径
        Returns:
            dict: 包含SSIM, PSNR, NIQE指标的字典
        """
        try:
            # 使用PIL读取图像
            orig_img = np.array(Image.open(original_path))
            gen_img = np.array(Image.open(generated_path))

            # 检查图像是否成功读取
            if orig_img.size == 0:
                print(f"错误: 无法读取原图: {original_path}")
                return None
            if gen_img.size == 0:
                print(f"错误: 无法读取生成图: {generated_path}")
                return None

            # 确保图像尺寸相同（这一步应该在预处理中已经完成）
            if orig_img.shape != gen_img.shape:
                print(f"警告: 图像尺寸不匹配 {os.path.basename(original_path)}")
                gen_img = np.array(Image.fromarray(gen_img).resize(
                    (orig_img.shape[1], orig_img.shape[0]), Image.Resampling.LANCZOS))

            # 计算各项指标
            psnr = self.calculate_psnr(orig_img, gen_img)
            ssim_score = self.calculate_ssim(orig_img, gen_img)
            niqe_score = self.calculate_niqe(gen_img)  # NIQE只需要生成图像

            return {
                'PSNR': psnr,
                'SSIM': ssim_score,
                'NIQE': niqe_score
            }
        except Exception as e:
            print(f"处理图像对时发生错误: {str(e)}")
            print(f"原图路径: {original_path}")
            print(f"生成图路径: {generated_path}")
            return None
    
    def print_results_table(self, results_df):
        """
        以表格形式打印结果

        Args:
            results_df: 包含评估结果的DataFrame
        """
        print("\n" + "="*80)
        print("图像质量评估结果")
        print("="*80)

        # 计算统计信息
        metrics = ['SSIM', 'PSNR', 'NIQE']
        stats = {}

        for metric in metrics:
            if metric in results_df.columns:
                values = results_df[metric].dropna()
                if len(values) > 0:
                    stats[metric] = {
                        '平均值': values.mean(),
                        '标准差': values.std(),
                        '最小值': values.min(),
                        '最大值': values.max()
                    }

        # 打印统计表格
        print(f"{'指标':<10} {'平均值':<12} {'标准差':<12} {'最小值':<12} {'最大值':<12}")
        print("-" * 80)

        for metric in metrics:
            if metric in stats:
                s = stats[metric]
                print(f"{metric:<10} {s['平均值']:<12.4f} {s['标准差']:<12.4f} "
                      f"{s['最小值']:<12.4f} {s['最大值']:<12.4f}")

        print("-" * 80)
        print(f"总共评估了 {len(results_df)} 对图像")
        print("="*80)
    
    def evaluate_all(self):
        """
        评估所有图像对的质量指标

        Returns:
            pandas.DataFrame: 包含所有评估结果的数据框
        """
        # 首先检查并调整图片尺寸
        self.check_and_resize_images()

        results = []
        valid_pairs = 0

        # 获取所有原始图片文件
        original_files = sorted([f for f in os.listdir(self.original_dir)
                               if f.lower().endswith(('.png', '.jpg', '.jpeg'))])

        print(f"\n开始评估 {len(original_files)} 对图像...")

        for filename in tqdm(original_files, desc="评估进度"):
            original_path = os.path.join(self.original_dir, filename)
            generated_path = os.path.join(self.generated_dir, filename)

            if not os.path.exists(generated_path):
                print(f"警告: 未找到对应的生成图像 {filename}")
                continue

            # 计算单对图像的指标
            metrics = self.evaluate_pair(original_path, generated_path)
            if metrics is None:
                print(f"跳过图像对: {filename}")
                continue

            # 添加文件名信息
            metrics['原图文件名'] = filename
            metrics['生成图文件名'] = filename
            results.append(metrics)
            valid_pairs += 1

        if valid_pairs == 0:
            print("错误: 没有成功处理任何图像对！")
            return None

        print(f"\n成功处理的图像对数量: {valid_pairs}")

        # 创建DataFrame
        df = pd.DataFrame(results)

        # 打印结果表格
        self.print_results_table(df)

        # 保存详细结果到CSV文件
        df.to_csv(self.output_path, index=False, encoding='utf-8-sig')
        print(f"\n详细结果已保存至: {self.output_path}")

        return df

if __name__ == "__main__":
    # 使用示例
    print("图像质量评估工具")
    print("支持的指标: SSIM (结构相似性), PSNR (峰值信噪比), NIQE (自然图像质量评估)")
    print("-" * 80)

    # 创建评估器实例
    evaluator = ImageEvaluator(
        original_dir="F:/论文/生成指标/评价指标/1/",  # 原始图片目录
        generated_dir="F:/论文/生成指标/评价指标/c/",  # 生成图片目录
        output_path="图像质量评估结果c.csv"  # 输出文件路径
    )

    # 开始评估
    try:
        results = evaluator.evaluate_all()
        if results is not None:
            print("\n评估完成！")
        else:
            print("\n评估失败，请检查输入目录和图片文件。")
    except Exception as e:
        print(f"\n评估过程中出现错误: {str(e)}")
        print("请检查输入路径是否正确，以及图片文件是否可以正常读取。")