如图所示，横轴是各个算法根据其在所有考察条件下的平均秩(Average Rank)进行的排序，平均秩越低，表明该算法的综合性能越优。图中上方红线是临界差(Critical Difference, CD)。这个CD值是根据显著性水平α、算法数量k以及考察条件数量N计算得出的。如果两个算法的平均秩之差小于CD值，则认为它们之间的性能在统计学上没有显著差异。在图中，这些性能相近的算法组被横轴下方一条水平的横线(clique线)连接起来。反之，如果两个算法的平均秩之差大于CD值，且它们没有被一条共同的横线连接，则表明它们之间的性能存在显著差异。

我们汇总了所提出的HyDR-Net以及所有对比方法在自建渗漏油分割数据集和Oil Spill数据集和Deep-SAR Oil Spill Dataset[27]这两个公开数据集上的F1分数、IOU和PA的评价结果。基于这些数据，我们首先进行了Friedman检验。结果表明，在α=0.1的显著性水平下，不同分割算法在综合性能上存在统计学上的显著差异，因此我们继续进行了Nemenyi后续检验。从图7中可以看出本文提出的HyDR-Net (Ours) 获得了最低的平均秩，**在所有参与比较的11种算法中位列第一**，这直观地显示了其在综合所有数据集和评价指标下的最优性能。

根据Nemenyi检验的结果，在α=0.1的显著性水平下，**计算得到的临界差CD值为4.6560**。其中HyDR-Net与DSACP、UPerNet(Twins)和TransUnet被同一条clique线连接，这四种方法共同构成了表现最优的第一梯队。此后，HyDR-Net与位列其后的UPerNet(Swin)的平均秩之差**为4.67，大于CD值4.6560**。因此，可以认为HyDR-Net在统计学上显著优于UPerNet(Swin)以及平均秩更靠后的SegFormer、DeeplabV3+、DMNet、ANN、KNet+PSPNet**和SAM(ViT-B)**。**特别值得注意的是，SAM(ViT-B)作为通用分割大模型，在本任务中获得了最高的平均秩，位列所有方法的最后一位，这进一步验证了通用大模型在特定领域任务中的适应性挑战，以及针对性设计专用模型的重要性。**因此HyDR-Net不仅在各项平均指标上领先，并且在综合考虑多个数据集和评价指标的严格统计比较下，其性能显著超越了大多数现有的先进分割方法，展现了其作为一种高效、鲁棒的渗漏油分割解决方案的潜力。





[1]赵振兵,欧阳文斌,冯烁,等.基于类内稀疏先验与改进YOLOv8的绝缘子红外图像检测方法[J/OL].图学学报,1-11[2025-07-29].http://kns.cnki.net/kcms/detail/10.1034.T.20250428.1048.002.html.

[1]涂晴昊,李元琪,刘一凡,等.基于扩散模型的文本生成材质贴图的泛化性优化方法[J].图学学报,2025,46(01):139-149.

[1]叶文龙,陈斌.PanoLoRA：基于Stable Diffusion的全景图像生成的高效微调方法[J/OL].图学学报,1-11[2025-07-29].http://kns.cnki.net/kcms/detail/10.1034.T.20250306.1926.006.html.





[28] 赵振兵, 欧阳文斌, 冯烁, 等. 基于类内稀疏先验与改进YOLOv8的绝缘子红外图像检测方法[J/OL]. 图学学报, 1-11[2025-07-29]. ZHAO Z B, OUYANG W B, FENG S, et al. Insulator infrared image detection method based on intra-class sparse prior and improved YOLOv8[J/OL]. Journal of Graphics, 1-11[2025-07-29](in Chinese).

[29] 涂晴昊, 李元琪, 刘一凡, 等. 基于扩散模型的文本生成材质贴图的泛化性优化方法[J]. 图学学报, 2025, 46(01): 139-149. TU Q H, LI Y Q, LIU Y F, et al. Generalization optimization method for text-generated material textures based on diffusion models[J]. Journal of Graphics, 2025, 46(01): 139-149(in Chinese).

[30] 叶文龙, 陈斌. PanoLoRA：基于Stable Diffusion的全景图像生成的高效微调方法[J/OL]. 图学学报, 1-11[2025-07-29]. YE W L, CHEN B. PanoLoRA: An efficient fine-tuning method for panoramic image generation based on Stable Diffusion[J/OL]. Journal of Graphics, 1-11[2025-07-29](in Chinese).

