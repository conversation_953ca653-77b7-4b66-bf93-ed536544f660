![image-20250724134520539](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250724134520539.png)

![image-20250724134528238](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250724134528238.png)

![image-20250724134553747](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250724134553747.png)

![image-20250724135810193](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250724135810193.png)

请结合我们刚才的讨论和下面我们的对比试验结果图表
图中数据是在我们本地服务器中跑出来的真实结果。
请深度思考，仔细分析，反复复盘，帮我补足图中表里面的空缺部分

| 方法              | F1     | IOU    | PA     | FPS  | Params  | FLOPs |
| ----------------- | ------ | ------ | ------ | ---- | ------- | ----- |
| ANN               | 73.61% | 58.24% | 88.71% | 2.31 | 43.93M  | 190G  |
| DeeeplabV3+       | 75.42% | 60.54% | 90.64% | 1.82 | 41.29M  | 182G  |
| UPerNet(Twins)    | 77.62% | 63.42% | 90.99% | 1.12 | 90.19M  | 275G  |
| KNet + PSPNet     | 71.85% | 56.06% | 89.13% | 1.84 | 59.89M  | 190G  |
| UPerNet(Swin)     | 73.80% | 58.47% | 90.58% | 1.04 | 122.88M | 306G  |
| DMNet             | 74.53% | 59.40% | 90.22% | 2.08 | 50.88M  | 201G  |
| TransUnet         | 75.78% | 61.01% | 90.41% | 1.98 | 91.52M  | 128G  |
| SegFormer         | 76.48% | 61.91% | 90.79% | 2.48 | 27.36M  | 56.9G |
| DSACP             | 77.95% | 63.87% | 91.38% | 1.72 | 42.48M  | 101G  |
| SAM2(Hiera-Small) | 68.99% | 41.28% | 85.26% | 1.25 | 46.04M  | 185G  |
| SAM(ViT-B)        | 72.50% | 55.80% | 88.9%  | 0.85 | 89.7M   | 654G  |
| Ours              | 80.46% | 67.31% | 92.15% | 1.57 | 32.83M  | 239G  |