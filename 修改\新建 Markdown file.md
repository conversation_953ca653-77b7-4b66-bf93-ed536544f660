| **提交时间:** | 2025-08-26    **修改稿截止日期:**  2025-09-10                |
| ------------- | ------------------------------------------------------------ |
| **修改意见:** | 论文存在以下问题：<br />1. 摘要中对算法性能的描述缺少量化数据。<br />2. 不恰当的表述，如“创新性的多尺度注意力校准模块(MAAM)”，“ 该网络通过创新性的判别式边界抗扰模块(DBAIM)”。<br />3. 补充数据集的具体情况，如原始图像的数目，数据增强图像的数目等。 <br />4. 格式不规范的地方，如变量未斜体。<br />5. 第2节中提到“标准扩散模型Dall-E 2、Stable Diffusion、Imagen生成的图像缺乏真实感，难以满足训练需求”，在第4节实验结果中并未进行对比实验，如何说明“难以满足训练需求”？ 6. 第2.1节提到“传统的数据增强方法无法生成具有真实效果的样本”，请问如何定义样本的真实效果，该叙述缺乏依据。 <br />7. 第2.2节提到“在面对渗漏油与积水的细微差异时，这些通用的特征提取和融合策略显得力不从心”，该叙述缺乏依据。 <br />8. 第4.2.2节提到“Friedman检验适用于比较多种算法在多个问题上的差异”，而本文主要针对渗漏油与高相似干扰物的判别与分割，采用此方法进行检验是否合理缺乏依据。 <br />9. 第4.2.3节中提到MAAM模块在渗漏油边界模糊时，会结合边缘信息和语义信息来精确识别轮廓，而图9第一行图像中，并未展现出加入MAAM模块后精确识别渗漏油轮廓，无法验证原文描述。   综上，论文修改后还需重审。为了便于审稿人重审，**修改稿请上传Word版**（请在修改稿中用颜色字体标注修改部分；同时附件上传详细修改说明）。 |

![image-20250828120651580](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250828120651580.png)

## 针对审稿意见1的修改

**原摘要：**
变电设备渗漏油的准确分割对于保障电力系统安全运行至关重要，然而，渗漏油与积水的高度视觉相似性、自身形态的不规则性以及此类干扰场景下训练数据的匮乏，对现有分割方法构成了严峻挑战。为应对这一挑战，本文提出了一种从数据增强到网络模型优化的综合解决方案。我们设计了一种新颖的基于扩散模型的时间步自适应调谐方法(EvoTune)，动态调整U-Net在图像生成过程中的特征贡献，显著提升了积水区域的生成质量与真实感，有效扩充了数据集中积水干扰场景的样本数量与多样性。其次，在数据增强的基础上，我们提出了一种高性能的渗漏油分割网络HyDR-Net。该网络通过创新性的判别式边界抗扰模块(DBAIM)，增强渗漏油与积水等易混淆背景的特征判别能力，并有效抑制背景噪声；以及创新性的多尺度注意力校准模块(MAAM)，对渗漏油特征进行多尺度上下文感知和精细化的边界校准，以适应渗漏油不规则的形态。大量实验结果表明，EvoTune显著提升了训练数据的质量；而HyDR-Net在各项关键评价指标上均大幅超越了现有主流分割方法，尤其在复杂积水干扰场景下展现出卓越的分割精度与鲁棒性。本研究为解决特定视觉干扰下的数据稀缺问题提供了有效途径，并为变电设备渗漏油的智能、精准检测提供了强有力的技术支撑。

**修改后摘要（加粗部分为新增内容）：**
变电设备渗漏油的准确分割对于保障电力系统安全运行至关重要，然而，渗漏油与积水的高度视觉相似性、自身形态的不规则性以及此类干扰场景下训练数据的匮乏，对现有分割方法构成了严峻挑战。为应对这一挑战，本文提出了一种从数据增强到网络模型优化的综合解决方案。我们设计了一种新颖的基于扩散模型的时间步自适应调谐方法(EvoTune)，动态调整U-Net在图像生成过程中的特征贡献，**在SSIM、PNSR、NIQE指标上分别达到0.9188、26.7902、5.7133，**显著提升了积水区域的生成质量与真实感，有效扩充了数据集中积水干扰场景的样本数量与多样性。其次，在数据增强的基础上，我们提出了一种高性能的渗漏油分割网络HyDR-Net。该网络通过判别式边界抗扰模块(DBAIM)，增强渗漏油与积水等易混淆背景的特征判别能力，并有效抑制背景噪声；以及多尺度注意力校准模块(MAAM)，对渗漏油特征进行多尺度上下文感知和精细化的边界校准，以适应渗漏油不规则的形态。大量实验结果表明，EvoTune显著提升了训练数据的质量；**而HyDR-Net在F1、PA指标上分别达到80.46%、92.15%，**在各项关键评价指标上均大幅超越了现有主流分割方法，尤其在复杂积水干扰场景下展现出卓越的分割精度与鲁棒性。本研究为解决特定视觉干扰下的数据稀缺问题提供了有效途径，并为变电设备渗漏油的智能、精准检测提供了强有力的技术支撑。

**英文摘要修改（加粗部分为新增内容）：**
Accurate segmentation of oil leakage from substation equipment is crucial for ensuring the safe operation of power systems. However, existing segmentation methods face significant challenges due to the high visual similarity between oil leakage and water accumulation, the irregular morphology of oil spills, and the scarcity of training data in such interference scenarios. To address these challenges, this paper proposes a comprehensive solution encompassing both data augmentation and network model optimization. We design a novel diffusion models-based timestep adaptive tuning method (EvoTune) that dynamically adjusts the feature contributions of U-Net during image generation, **achieving SSIM, PNSR, and NIQE scores of 0.9188, 26.7902, and 5.7133 respectively,** significantly improving the generation quality and realism of water accumulation regions while effectively expanding the quantity and diversity of water interference scenarios in the dataset. Building upon this data augmentation foundation, we propose HyDR-Net (Hydro Discriminative Refining Network), a high-performance oil leakage segmentation network. The network incorporates a Discriminative Boundary Anti-interference Module (DBAIM) that enhances feature discrimination between oil leakage and confusing backgrounds such as water accumulation while effectively suppressing background noise. Additionally, a Multi-Scale Attentive Alignment Module (MAAM) performs multi-scale context-aware processing and fine-grained boundary calibration of oil leakage features to accommodate the irregular morphology of oil spills. Extensive experimental results demonstrate that EvoTune significantly improves training data quality, **while HyDR-Net achieves F1 and PA scores of 80.46% and 92.15% respectively,** substantially outperforming existing mainstream segmentation methods across all key evaluation metrics, particularly exhibiting superior segmentation accuracy and robustness in complex water interference scenarios. This research provides an effective approach for addressing data scarcity under specific visual interference conditions and offers robust technical support for intelligent and precise detection of oil leakage in substation equipment.